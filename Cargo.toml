[package]
name = "subnet-dpn-websocket"
version = "0.1.0"
edition = "2024"

[dependencies]
anyhow = "1.0.98"
futures = "0.3.31"
tokio = { version = "1.37.0", features = ["full"] }
tokio-tungstenite = "0.21.0"
tungstenite = "0.21.0"
serde = { version = "1.0.219", features = ["derive"] }
static_init = "1.0.3"
serde_yaml = "0.9.34"
tracing = "0.1.41"
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }
# redis = {version = "0.30.0", features = ["tokio-comp", "connection-manager", "json"]}
serde_json = "1.0.140"
thiserror = "2.0.12"
config = "0.15.11"
dotenv = "0.15.0"
lapin = { version = "2.5.3", features = ["native-tls"] }
async-trait = "0.1.88"
chrono = "0.4.40"
num-traits = "0.2"
num-derive = "0.4"
utoipa = "5.3.1"
reqwest = { version = "0.11", features = ["json"] }
bitcode = "=0.6.5"
native-tls = "0.2.14"
tcp-stream = { version = "0.28.0", features = ["native-tls"] }