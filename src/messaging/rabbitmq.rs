use anyhow::{Result, Context};
// use bitcode::decode;
// use async_trait::async_trait;
use lapin::{
    Connection, ConnectionProperties, Channel,
    options::{BasicConsumeOptions, QueueDeclareOptions},
    types::FieldTable,
    uri::AMQPUri,
};
use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::{error, info, debug};
use futures::stream::StreamExt;
use native_tls::TlsConnector;
use tcp_stream::{HandshakeResult, TcpStream, NativeTlsConnector};

use crate::messaging::events::{DPNEvent, MsgQueueEvent, SessionEvent, SessionStats};
// use crate::error::MessagingError;

#[derive(Clone)]
pub struct RabbitMQClient {
    connection: Arc<Connection>,
    channel: Arc<Channel>,
}

impl RabbitMQClient {
    pub async fn new(rabbitmq_url: &str) -> Result<Self> {
        let uri: AMQPUri = rabbitmq_url.parse()
            .map_err(|e| anyhow::anyhow!("Failed to parse RabbitMQ URL: {}", e))?;

        // Create TLS connector that accepts invalid certificates for AWS RabbitMQ
        let tls_connector = TlsConnector::builder()
            .danger_accept_invalid_certs(true)
            .danger_accept_invalid_hostnames(true)
            .build()
            .context("Failed to create TLS connector")?;

        let properties = ConnectionProperties::default();

        loop {
            // Create a custom connector function that uses our TLS configuration
            let connector = {
                let tls_connector = tls_connector.clone();
                Box::new(move |uri: &AMQPUri| -> HandshakeResult {
                    let host = uri.authority.host.clone();
                    let port = uri.authority.port;

                    // Create TCP stream
                    let stream = std::net::TcpStream::connect((host.as_str(), port))
                        .map_err(|e| tcp_stream::HandshakeError::Failure(e.into()))?;

                    // Convert to tcp_stream::TcpStream (true means connected)
                    let tcp_stream = TcpStream::Plain(stream, true);

                    // Create NativeTlsConnector from our TlsConnector
                    let native_tls_connector = NativeTlsConnector::from(tls_connector);

                    // Perform TLS handshake using native-tls
                    tcp_stream.into_native_tls(&native_tls_connector, &host)
                })
            };

            match Connection::connector(
                uri.clone(),
                connector,
                properties.clone(),
            ).await {
                Ok(connection) => {
                    info!("Created RabbitMQ channel for session events queue: {}", "session-events_websocket");

                    let channel = connection.create_channel().await
                        .context("Failed to create RabbitMQ channel")?;
                    return Ok(Self {
                        connection: Arc::new(connection),
                        channel: Arc::new(channel),
                    })
                },
                Err(e) => {
                    error!("Failed to connect to RabbitMQ: {}", e);
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                    continue;
                }
            }

        }
    }

    pub async fn start_consuming(&self, queue_name: &str) -> Result<mpsc::Receiver<MsgQueueEvent>> {
        let (tx, rx) = mpsc::channel(1000);

        // Declare the queue with durable=true to match existing queue configuration
        self.channel.queue_declare(
            queue_name,
            QueueDeclareOptions {
                durable: true,
                ..QueueDeclareOptions::default()
            },
            FieldTable::default(),
        ).await.context("Failed to declare queue")?;

        // Start consuming messages
        let mut consumer = self.channel.basic_consume(
            queue_name,
            "",
            BasicConsumeOptions::default(),
            FieldTable::default(),
        ).await.context("Failed to start consuming")?;

        // Clone queue_name to avoid lifetime issues
        let queue_name = queue_name.to_string();

        // Process incoming messages
        tokio::spawn(async move {
            info!("Started consuming from queue: {}", queue_name);

            while let Some(delivery) = consumer.next().await {
                match delivery {
                    Ok(delivery) => {
                        let payload = String::from_utf8_lossy(&delivery.data);

                        // First try to parse as DPNEvent
                        if let Ok(event) = serde_json::from_str::<DPNEvent>(&payload) {
                            debug!("Received DPNEvent: {:?}", event);
                            match event {
                                DPNEvent::SessionCreated(extra) => {
                                    if let Err(e) = tx.send(MsgQueueEvent::SessionEvent(SessionEvent::SessionCreated(extra))).await {
                                        error!("Failed to send event to channel: {}", e);
                                    }
                                },
                                DPNEvent::SessionTerminated(extra) => {
                                    if let Err(e) = tx.send(MsgQueueEvent::SessionEvent(SessionEvent::SessionTerminated(extra))).await {
                                        error!("Failed to send event to channel: {}", e);
                                    }
                                }
                            }
                        }
                        // Then try to parse as SessionStats, using bitcode
                        else if let Ok(stats) =  serde_json::from_str::<SessionStats>(&payload) {
                            debug!("Received SessionStats: {:?}", stats);
                            if let Err(e) = tx.send(MsgQueueEvent::SessionStats(stats)).await {
                                error!("Failed to send stats to channel: {}", e);
                            }
                        }
                        else {
                            error!(
                                "Failed to decode message: {:#?}",
                                payload
                            );
                        }


                        // Acknowledge the message
                        if let Err(e) = delivery.ack(Default::default()).await {
                            error!("Failed to acknowledge delivery: {}", e);
                        }
                    },
                    Err(e) => {
                        error!("Failed to receive delivery: {}", e);
                    }
                }
            }
        });

        Ok(rx)
    }
}